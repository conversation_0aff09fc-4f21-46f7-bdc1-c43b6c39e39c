'use client';

import React from 'react';
import { useFamilyMembers } from '@/hooks/useFamily';
import { useFamilyRole } from '@/hooks/useAuth';
import CalendarDashboard from '@/components/calendar/CalendarDashboard';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import Link from 'next/link';

interface CalendarPageProps {
  params: Promise<{ familyId: string }>;
}

export default function CalendarPage({ params }: CalendarPageProps) {
  const resolvedParams = React.use(params);
  const { familyId } = resolvedParams;

  const { members, loading: membersLoading } = useFamilyMembers(familyId);
  const { familyMember, loading: authLoading } = useFamilyRole(familyId);

  if (membersLoading || authLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Link href={`/families/${familyId}`}>
            <Button variant="outline" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Powrót do dashboard
            </Button>
          </Link>
          <h1 className="text-2xl font-bold">Kalendarz</h1>
        </div>

        <div className="text-center py-12">
          <p className="text-gray-500">Ładowanie kalendarza...</p>
        </div>
      </div>
    );
  }

  if (!familyMember) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Link href={`/families/${familyId}`}>
            <Button variant="outline" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Powrót do dashboard
            </Button>
          </Link>
          <h1 className="text-2xl font-bold">Kalendarz</h1>
        </div>

        <div className="text-center py-12">
          <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto">
            <h3 className="text-lg font-semibold text-red-900 mb-2">Wystąpił błąd</h3>
            <p className="text-red-700">Brak dostępu do tej rodziny</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Link href={`/families/${familyId}`}>
          <Button variant="outline" size="sm">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Powrót do dashboard
          </Button>
        </Link>
        <h1 className="text-2xl font-bold">Kalendarz rodziny</h1>
      </div>

      <div className="h-[calc(100vh-12rem)]">
        <CalendarDashboard familyId={familyId} members={members} className="h-full" />
      </div>
    </div>
  );
}

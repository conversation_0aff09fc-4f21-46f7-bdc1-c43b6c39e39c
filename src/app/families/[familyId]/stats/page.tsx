'use client';

import React from 'react';
import { useFamilyRole, useCanManageFamily } from '@/hooks/useAuth';
import StatsDashboard from '@/components/stats/StatsDashboard';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import Link from 'next/link';

interface StatsPageProps {
  params: Promise<{ familyId: string }>;
}

export default function StatsPage({ params }: StatsPageProps) {
  const resolvedParams = React.use(params);
  const { familyId } = resolvedParams;

  const { familyMember, loading: authLoading } = useFamilyRole(familyId);
  const { canViewStatistics } = useCanManageFamily(familyId);

  if (authLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Link href={`/families/${familyId}`}>
            <Button variant="outline" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Powrót do dashboard
            </Button>
          </Link>
          <h1 className="text-2xl font-bold">Statystyki</h1>
        </div>

        <div className="text-center py-12">
          <p className="text-gray-500">Ładowanie...</p>
        </div>
      </div>
    );
  }

  if (!familyMember) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Link href={`/families/${familyId}`}>
            <Button variant="outline" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Powrót do dashboard
            </Button>
          </Link>
          <h1 className="text-2xl font-bold">Statystyki</h1>
        </div>

        <div className="text-center py-12">
          <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto">
            <h3 className="text-lg font-semibold text-red-900 mb-2">Wystąpił błąd</h3>
            <p className="text-red-700">Brak dostępu do tej rodziny</p>
          </div>
        </div>
      </div>
    );
  }

  if (!authLoading && !canViewStatistics) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Link href={`/families/${familyId}`}>
            <Button variant="outline" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Powrót do dashboard
            </Button>
          </Link>
          <h1 className="text-2xl font-bold">Statystyki</h1>
        </div>

        <div className="text-center py-12">
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 max-w-md mx-auto">
            <h3 className="text-lg font-semibold text-yellow-900 mb-2">Brak uprawnień</h3>
            <p className="text-yellow-700">
              Tylko właściciele, rodzice i opiekunowie mogą przeglądać statystyki rodziny.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Link href={`/families/${familyId}`}>
          <Button variant="outline" size="sm">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Powrót do dashboard
          </Button>
        </Link>
        <h1 className="text-2xl font-bold">Statystyki rodziny</h1>
      </div>

      <StatsDashboard familyId={familyId} />
    </div>
  );
}

'use client';

import React from 'react';
import { useFamilyMembers } from '@/hooks/useFamily';
import { useFamilyRole, useCanManageFamily, useCanManageTasks } from '@/hooks/useAuth';
import { useTasks } from '@/hooks/useTasks';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Users, CheckSquare, Calendar, TrendingUp, UserPlus, Mail } from 'lucide-react';
import Link from 'next/link';

interface FamilyPageProps {
    params: Promise<{ familyId: string }>;
}

export default function FamilyPage({ params }: FamilyPageProps) {
    const resolvedParams = React.use(params);
    const { familyId } = resolvedParams;

    const { members, loading: membersLoading } = useFamilyMembers(familyId);
    const { familyMember, loading: authLoading } = useFamilyRole(familyId);
    const { canViewStatistics, canManageMembers } = useCanManageFamily(familyId);
    const { canCreateTasks } = useCanManageTasks(familyId);
    const { tasks, loading: tasksLoading } = useTasks(familyId);

    const familyName = familyMember?.family?.name || 'Rodzina';

    if (membersLoading || authLoading) {
        return (
            <div className="space-y-6">
                <div className="flex items-center gap-4">
                    <Link href="/dashboard">
                        <Button variant="outline" size="sm">
                            <ArrowLeft className="mr-2 h-4 w-4" />
                            Powrót do dashboard
                        </Button>
                    </Link>
                    <h1 className="text-2xl font-bold">Ładowanie...</h1>
                </div>

                <div className="text-center py-12">
                    <p className="text-gray-500">Ładowanie danych rodziny...</p>
                </div>
            </div>
        );
    }

    if (!familyMember) {
        return (
            <div className="space-y-6">
                <div className="flex items-center gap-4">
                    <Link href="/dashboard">
                        <Button variant="outline" size="sm">
                            <ArrowLeft className="mr-2 h-4 w-4" />
                            Powrót do dashboard
                        </Button>
                    </Link>
                    <h1 className="text-2xl font-bold">Błąd</h1>
                </div>

                <div className="text-center py-12">
                    <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto">
                        <h3 className="text-lg font-semibold text-red-900 mb-2">Wystąpił błąd</h3>
                        <p className="text-red-700">Brak dostępu do tej rodziny</p>
                    </div>
                </div>
            </div>
        );
    }

    const pendingTasks = tasks?.filter(task => task.status === 'ACTIVE') || [];
    const completedTasks = tasks?.filter(task => task.status === 'COMPLETED') || [];

    return (
        <div className="space-y-6">
            <div className="flex items-center gap-4">
                <Link href="/dashboard">
                    <Button variant="outline" size="sm">
                        <ArrowLeft className="mr-2 h-4 w-4" />
                        Powrót do dashboard
                    </Button>
                </Link>
                <h1 className="text-2xl font-bold">{familyName}</h1>
            </div>

            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                {/* Family Members Card */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Users className="h-5 w-5" />
                            Członkowie rodziny
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-2 mb-4">
                            <div className="flex justify-between text-sm">
                                <span>Liczba członków:</span>
                                <span className="font-medium">{members.length}</span>
                            </div>
                            <div className="text-sm text-gray-600">
                                Twoja rola: <span className="font-medium">{familyMember.role}</span>
                            </div>
                            <div className="text-sm text-gray-600">
                                Twoje punkty: <span className="font-medium">{familyMember.points}</span>
                            </div>
                        </div>
                        <div className="space-y-2">
                            <Link href={`/families/${familyId}/members`}>
                                <Button variant="outline" size="sm" className="w-full">
                                    <Users className="mr-2 h-4 w-4" />
                                    Zarządzaj członkami
                                </Button>
                            </Link>
                            {canManageMembers && (
                                <Link href={`/families/${familyId}/invitations`}>
                                    <Button variant="outline" size="sm" className="w-full">
                                        <UserPlus className="mr-2 h-4 w-4" />
                                        Zaproszenia
                                    </Button>
                                </Link>
                            )}
                        </div>
                    </CardContent>
                </Card>

                {/* Tasks Overview Card */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <CheckSquare className="h-5 w-5" />
                            Zadania
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-2 mb-4">
                            <div className="flex justify-between text-sm">
                                <span>Oczekujące:</span>
                                <span className="font-medium text-orange-600">{pendingTasks.length}</span>
                            </div>
                            <div className="flex justify-between text-sm">
                                <span>Ukończone:</span>
                                <span className="font-medium text-green-600">{completedTasks.length}</span>
                            </div>
                            <div className="flex justify-between text-sm">
                                <span>Łącznie:</span>
                                <span className="font-medium">{tasks?.length || 0}</span>
                            </div>
                        </div>
                        <Link href={`/families/${familyId}/tasks`}>
                            <Button variant="outline" size="sm" className="w-full">
                                <CheckSquare className="mr-2 h-4 w-4" />
                                Przeglądaj zadania
                            </Button>
                        </Link>
                    </CardContent>
                </Card>

                {/* Calendar Card */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Calendar className="h-5 w-5" />
                            Kalendarz
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-2 mb-4">
                            <p className="text-sm text-gray-600">
                                Przeglądaj kalendarz rodziny z zadaniami i wydarzeniami.
                            </p>
                        </div>
                        <Link href={`/families/${familyId}/calendar`}>
                            <Button variant="outline" size="sm" className="w-full">
                                <Calendar className="mr-2 h-4 w-4" />
                                Otwórz kalendarz
                            </Button>
                        </Link>
                    </CardContent>
                </Card>

                {/* Statistics Card */}
                {canViewStatistics && (
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <TrendingUp className="h-5 w-5" />
                                Statystyki
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-2 mb-4">
                                <p className="text-sm text-gray-600">
                                    Przeglądaj statystyki wydajności rodziny i indywidualnych członków.
                                </p>
                            </div>
                            <Link href={`/families/${familyId}/stats`}>
                                <Button variant="outline" size="sm" className="w-full">
                                    <TrendingUp className="mr-2 h-4 w-4" />
                                    Zobacz statystyki
                                </Button>
                            </Link>
                        </CardContent>
                    </Card>
                )}

                {/* Recent Activity Card */}
                <Card className="md:col-span-2 lg:col-span-2">
                    <CardHeader>
                        <CardTitle>Ostatnia aktywność</CardTitle>
                    </CardHeader>
                    <CardContent>
                        {tasksLoading ? (
                            <p className="text-gray-500">Ładowanie aktywności...</p>
                        ) : pendingTasks.length > 0 ? (
                            <div className="space-y-3">
                                <p className="text-sm text-gray-600 mb-3">Najbliższe zadania do wykonania:</p>
                                {pendingTasks.slice(0, 3).map(task => (
                                    <div
                                        key={task.id}
                                        className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                                    >
                                        <div>
                                            <div className="font-medium">{task.title}</div>
                                            <div className="text-sm text-gray-600">
                                                {task.assignments && task.assignments.length > 0
                                                    ? `Przypisane: ${task.assignments[0].member.user.firstName}`
                                                    : 'Nieprzypisane'}
                                            </div>
                                        </div>
                                        <div className="text-sm text-gray-500">{task.points} pkt</div>
                                    </div>
                                ))}
                                {pendingTasks.length > 3 && (
                                    <p className="text-sm text-gray-500 text-center">
                                        i {pendingTasks.length - 3} więcej...
                                    </p>
                                )}
                            </div>
                        ) : (
                            <p className="text-gray-500 text-center py-4">Brak oczekujących zadań</p>
                        )}
                    </CardContent>
                </Card>
            </div>
        </div>
    );
}

'use client';

import { cn } from '@/lib/utils';
import { Container, Grid, GridItem } from '@/components/ui/Grid';
import { Sidebar } from '@/components/layout/Sidebar';
import { Header } from '@/components/layout/Header';
import { MobileHeader } from '@/components/mobile/MobileHeader';
import { MobileTabBar } from '@/components/mobile/MobileTabBar';
import { SidebarProvider, useSidebar } from '@/contexts/SidebarContext';

interface DashboardLayoutProps {
  children: React.ReactNode;
  familyId?: string;
  user?: any;
  families?: any[];
  currentFamilyId?: string;
  className?: string;
  title?: string;
  showMobileHeader?: boolean;
  showSidebar?: boolean;
}

function DashboardLayoutInner({
  children,
  familyId,
  user,
  families = [],
  currentFamilyId,
  className,
  title,
  showMobileHeader = true,
  showSidebar = true,
}: DashboardLayoutProps) {
  const { isCollapsed } = useSidebar();

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Desktop Layout */}
      <div className="hidden lg:block">
        {/* Sidebar */}
        {showSidebar && (
          <Sidebar
            familyId={currentFamilyId}
            user={user}
            families={families}
            currentFamilyId={currentFamilyId}
          />
        )}

        {/* Main Content */}
        <div
          className={cn(
            'flex flex-col min-h-screen transition-all duration-300',
            showSidebar ? (isCollapsed ? 'ml-16' : 'ml-64') : 'ml-0'
          )}
        >
          {/* Desktop Header */}
          <Header
            currentFamily={families?.find(f => f.family?.id === currentFamilyId)?.family}
            user={user}
            families={families}
            currentFamilyId={currentFamilyId}
            className="lg:block"
          />

          {/* Content Area */}
          <main className="flex-1 overflow-auto">
            <Container size="full" padding className={cn('py-8', className)}>
              {children}
            </Container>
          </main>
        </div>
      </div>

      {/* Mobile/Tablet Layout */}
      <div className="lg:hidden">
        {/* Mobile Header */}
        {showMobileHeader && (
          <MobileHeader
            title={title}
            user={user}
            families={families}
            currentFamilyId={currentFamilyId}
          />
        )}

        {/* Content Area */}
        <main className="pb-20">
          {' '}
          {/* Space for bottom tab bar */}
          <Container
            size="full"
            padding={false}
            className={cn('min-h-screen bg-gray-50', className)}
          >
            <div className="mobile-spacing">{children}</div>
          </Container>
        </main>

        {/* Mobile Tab Bar */}
        <MobileTabBar familyId={currentFamilyId} />
      </div>
    </div>
  );
}

export function DashboardLayout(props: DashboardLayoutProps) {
  return (
    <SidebarProvider>
      <DashboardLayoutInner {...props} />
    </SidebarProvider>
  );
}

// Grid Layout for Dashboard Cards
interface DashboardGridProps {
  children: React.ReactNode;
  cols?: 1 | 2 | 3 | 4;
  className?: string;
}

export function DashboardGrid({
  children,
  cols = 'responsive' as any,
  className,
}: DashboardGridProps) {
  return (
    <Grid cols={cols} gap="lg" className={cn('mb-8', className)}>
      {children}
    </Grid>
  );
}

// Dashboard Card Wrapper
interface DashboardCardProps {
  children: React.ReactNode;
  span?: 1 | 2 | 3 | 4 | 'full';
  className?: string;
}

export function DashboardCard({ children, span, className }: DashboardCardProps) {
  return (
    <GridItem span={span} className={className}>
      {children}
    </GridItem>
  );
}

// Dashboard Section
interface DashboardSectionProps {
  title: string;
  description?: string;
  children: React.ReactNode;
  action?: React.ReactNode;
  className?: string;
}

export function DashboardSection({
  title,
  description,
  children,
  action,
  className,
}: DashboardSectionProps) {
  return (
    <section className={cn('space-y-6', className)}>
      {/* Section Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-h2 font-display text-gray-900">{title}</h2>
          {description && <p className="text-gray-600 font-body mt-1">{description}</p>}
        </div>
        {action && <div className="flex items-center gap-2">{action}</div>}
      </div>

      {/* Section Content */}
      <div>{children}</div>
    </section>
  );
}

// Stats Row for Dashboard
interface StatsRowProps {
  children: React.ReactNode;
  className?: string;
}

export function StatsRow({ children, className }: StatsRowProps) {
  return (
    <div className={cn('grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4', className)}>
      {children}
    </div>
  );
}

// Stat Card
interface StatCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  color?: 'blue' | 'green' | 'red' | 'yellow' | 'purple' | 'orange';
  trend?: {
    value: number;
    isPositive: boolean;
  };
  className?: string;
}

export function StatCard({ title, value, icon, color = 'blue', trend, className }: StatCardProps) {
  const colorClasses = {
    blue: 'text-blue-500',
    green: 'text-green-500',
    red: 'text-red-500',
    yellow: 'text-yellow-500',
    purple: 'text-purple-500',
    orange: 'text-orange-500',
  };

  return (
    <div className={cn('bg-white rounded-lg p-4 card-shadow hover-lift transition-all', className)}>
      <div className="flex items-center gap-3">
        <div className={cn('flex-shrink-0', colorClasses[color])}>{icon}</div>
        <div className="min-w-0 flex-1">
          <p className="text-sm text-gray-600 truncate">{title}</p>
          <div className="flex items-center gap-2">
            <p className="text-2xl font-bold text-gray-900">{value}</p>
            {trend && (
              <span
                className={cn(
                  'text-xs font-medium',
                  trend.isPositive ? 'text-green-600' : 'text-red-600'
                )}
              >
                {trend.isPositive ? '+' : ''}
                {trend.value}%
              </span>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

/**
 * Unified Navigation System for FamilyTasks Application
 *
 * This module implements a consistent navigation structure across desktop and mobile devices.
 *
 * NAVIGATION HIERARCHY:
 *
 * Primary Navigation (max 5 items for mobile compatibility):
 * - Dashboard: Family overview and stats
 * - Zadania: Task management and tracking
 * - Kalendarz: Calendar and scheduling
 * - Statystyki: Analytics and insights
 * - Członkowie: Family member management
 *
 * Secondary Navigation (overflow/settings menu):
 * - Zaproszenia: Invitation management
 * - Ustawienia: Family settings
 * - Profil: User profile settings
 *
 * URL STRUCTURE:
 * - Unified pattern: /families/${familyId}/${section}
 * - Consistent across all platforms and components
 *
 * RESPONSIVE BEHAVIOR:
 * - Desktop: Full navigation in sidebar + header
 * - Mobile: Primary navigation in bottom tab bar, secondary in hamburger menu
 * - Touch targets optimized for accessibility (min 44px)
 *
 * STYLING CONSISTENCY:
 * - Active state: text-purple-600, bg-purple-50
 * - Hover state: hover:text-purple-600, hover:bg-purple-50
 * - Inactive state: text-gray-700
 * - Transitions: duration-200 for smooth interactions
 */

'use client';

import { usePathname } from 'next/navigation';
import Link from 'next/link';
import { cn } from '@/lib/utils';
import {
    Home,
    Star,
    Users,
    Calendar,
    BarChart3,
    Settings,
    CheckSquare,
    Clock,
    User,
    Bell,
    House,
    LayoutDashboard,
    LogOut,
} from 'lucide-react';

interface NavigationItem {
    name: string;
    href: string;
    icon: any;
    description?: string;
    badge?: string | number;
    external?: boolean;
}

interface NavigationProps {
    familyId?: string;
    className?: string;
    variant?: 'desktop' | 'mobile' | 'sidebar';
    onItemClick?: () => void;
    families?: any[]; // For settings context to build dashboard link
}

/**
 * Main Navigation Component
 *
 * Renders navigation items based on context and variant:
 * - Landing: Shows public navigation for non-authenticated users
 * - Desktop/Sidebar: Shows full navigation hierarchy
 * - Mobile: Shows only primary navigation (for bottom tab bar)
 *
 * @param familyId - Current family ID for generating family-specific URLs
 * @param className - Additional CSS classes
 * @param variant - Navigation style variant (desktop|mobile|sidebar)
 * @param onItemClick - Callback for navigation item clicks (used for closing mobile menus)
 */
export function Navigation({
    familyId,
    className,
    variant = 'desktop',
    onItemClick,
    families = [],
}: NavigationProps) {
    const pathname = usePathname();

    // Landing page navigation
    const landingNavigation: NavigationItem[] = [
        {
            name: 'Strona główna',
            href: '/',
            icon: Home,
            description: 'Powrót do strony głównej',
        },
        {
            name: 'Funkcje',
            href: '/features',
            icon: Star,
            description: 'Poznaj wszystkie funkcje',
        },
        {
            name: 'O nas',
            href: '/about',
            icon: Users,
            description: 'Dowiedz się więcej o nas',
        },
    ];

    // Settings page navigation (for user settings context)
    const firstFamilyId = families?.[0]?.family?.id;
    const dashboardHref = firstFamilyId ? `/families/${firstFamilyId}` : '/';

    const settingsNavigation: NavigationItem[] = [
        {
            name: 'Dashboard',
            href: dashboardHref,
            icon: Home,
            description: 'Powrót do dashboard',
        },
        {
            name: 'Profil użytkownika',
            href: '/settings/profile',
            icon: User,
            description: 'Zarządzaj swoim profilem',
        },
        {
            name: 'Powiadomienia',
            href: '/settings/notifications',
            icon: Bell,
            description: 'Ustawienia powiadomień',
        },
        {
            name: 'Bezpieczeństwo',
            href: '/settings/security',
            icon: Settings,
            description: 'Bezpieczeństwo konta',
        },
        {
            name: 'Prywatność',
            href: '/settings/privacy',
            icon: Settings,
            description: 'Ustawienia prywatności',
        },
    ];

    // Primary navigation - core features (max 5 for mobile compatibility)
    const primaryNavigation: NavigationItem[] = familyId
        ? [
            {
                name: 'Home',
                href: '/dashboard',
                icon: House,
                description: 'Strona główna',
            },
            {
                name: 'Dashboard',
                href: `/families/${familyId}`,
                icon: LayoutDashboard,
                description: 'Przegląd rodziny',
            },
            {
                name: 'Zadania',
                href: `/families/${familyId}/tasks`,
                icon: CheckSquare,
                description: 'Zarządzaj zadaniami',
            },
            {
                name: 'Kalendarz',
                href: `/families/${familyId}/calendar`,
                icon: Calendar,
                description: 'Planuj wydarzenia',
            },
            {
                name: 'Statystyki',
                href: `/families/${familyId}/stats`,
                icon: BarChart3,
                description: 'Analizuj postępy',
            },
            {
                name: 'Członkowie',
                href: `/families/${familyId}/members`,
                icon: Users,
                description: 'Członkowie rodziny',
            },
        ]
        : [];

    // Secondary navigation - settings and management
    const secondaryNavigation: NavigationItem[] = familyId
        ? [
            {
                name: 'Zaproszenia',
                href: `/families/${familyId}/invitations`,
                icon: Clock,
                description: 'Zarządzaj zaproszeniami',
            },
            {
                name: 'Ustawienia',
                href: `/families/${familyId}/settings`,
                icon: Settings,
                description: 'Konfiguracja rodziny',
            },
            {
                name: 'Profil',
                href: '/settings',
                icon: User,
                description: 'Ustawienia osobiste',
            },
            {
                name: 'Wyloguj się',
                href: '/auth/sign-out',
                icon: LogOut,
                description: 'Zakończ sesję',
            },
        ]
        : [];

    // Combined navigation for desktop/sidebar view
    const dashboardNavigation = [...primaryNavigation, ...secondaryNavigation];

    // Select navigation items based on variant and context
    const getNavigationItems = () => {
        // Settings page context - show settings navigation
        if (pathname.startsWith('/settings')) {
            return settingsNavigation;
        }

        if (!familyId) {
            return landingNavigation;
        }

        // Mobile uses only primary navigation (bottom tab bar pattern)
        if (variant === 'mobile') {
            return primaryNavigation;
        }

        // Desktop and sidebar use full navigation
        return dashboardNavigation;
    };

    const navigationItems = getNavigationItems();

    const isActive = (href: string) => {
        if (href === '/') {
            return pathname === '/';
        }
        return pathname.startsWith(href);
    };

    const baseItemClasses =
        'group relative flex items-center gap-2 rounded-lg transition-all duration-200';

    const getItemClasses = (href: string, variant: string) => {
        const active = isActive(href);

        const variantClasses = {
            desktop: cn(
                baseItemClasses,
                'px-2 py-2 text-sm font-medium',
                active
                    ? 'text-purple-600 bg-purple-50'
                    : 'text-gray-700 hover:text-purple-600 hover:bg-purple-50'
            ),
            mobile: cn(
                baseItemClasses,
                'px-2 pb-2 text-base font-medium',
                active
                    ? 'text-purple-600 bg-purple-50 border-r-2 border-purple-600'
                    : 'text-gray-700 hover:text-purple-600 hover:bg-purple-50'
            ),
            sidebar: cn(
                baseItemClasses,
                'px-2 pb-2 text-sm font-medium',
                active
                    ? 'text-purple-600 bg-purple-50 shadow-sm'
                    : 'text-gray-700 hover:text-purple-600 hover:bg-gray-50'
            ),
        };

        return variantClasses[variant as keyof typeof variantClasses];
    };

    if (variant === 'mobile') {
        return (
            <nav className={cn('space-y-1', className)}>
                {navigationItems.map(item => (
                    <Link
                        key={item.name}
                        href={item.href}
                        onClick={onItemClick}
                        className={getItemClasses(item.href, variant)}
                    >
                        <item.icon className="h-5 w-5 shrink-0" />
                        <div className="flex-1 min-w-0">
                            <div>{item.name}</div>
                            {item.description && (
                                <div className="text-xs text-gray-500">{item.description}</div>
                            )}
                        </div>
                        {item.badge && (
                            <div className="bg-purple-100 text-purple-600 text-xs px-2 py-1 rounded-full">
                                {item.badge}
                            </div>
                        )}
                    </Link>
                ))}
            </nav>
        );
    }

    if (variant === 'sidebar') {
        return (
            <nav className={cn('space-y-1', className)}>
                {navigationItems.map(item => (
                    <Link
                        key={item.name}
                        href={item.href}
                        onClick={onItemClick}
                        className={getItemClasses(item.href, variant)}
                        title={item.description}
                    >
                        <item.icon className="h-4 w-4 shrink-0" />
                        <span className="truncate">{item.name}</span>
                        {item.badge && (
                            <div className="bg-purple-100 text-purple-600 text-xs px-1.5 py-0.5 rounded-full ml-auto">
                                {item.badge}
                            </div>
                        )}
                    </Link>
                ))}
            </nav>
        );
    }

    // Desktop horizontal navigation
    return (
        <nav className={cn('flex items-center space-x-6', className)}>
            {navigationItems.map(item => (
                <Link
                    key={item.name}
                    href={item.href}
                    onClick={onItemClick}
                    className={getItemClasses(item.href, variant)}
                    title={item.description}
                >
                    <item.icon className="h-4 w-4 shrink-0" />
                    <span>{item.name}</span>
                    {item.badge && (
                        <div className="bg-purple-100 text-purple-600 text-xs px-1.5 py-0.5 rounded-full ml-1">
                            {item.badge}
                        </div>
                    )}
                </Link>
            ))}
        </nav>
    );
}

// Breadcrumb Navigation Component
interface BreadcrumbItem {
    name: string;
    href?: string;
    current?: boolean;
}

interface BreadcrumbNavigationProps {
    items: BreadcrumbItem[];
    className?: string;
}

export function BreadcrumbNavigation({ items, className }: BreadcrumbNavigationProps) {
    return (
        <nav className={cn('flex items-center space-x-1 text-sm text-gray-500', className)}>
            {items.map((item, index) => (
                <div key={item.name} className="flex items-center">
                    {index > 0 && <span className="mx-2 text-gray-400">/</span>}
                    {item.href && !item.current ? (
                        <Link href={item.href} className="hover:text-purple-600 transition-colors">
                            {item.name}
                        </Link>
                    ) : (
                        <span className={item.current ? 'text-gray-900 font-medium' : ''}>{item.name}</span>
                    )}
                </div>
            ))}
        </nav>
    );
}

/**
 * Utility Functions for External Components
 *
 * These functions allow other components (Sidebar, MobileTabBar, MobileHeader)
 * to access the same navigation data ensuring consistency across the application.
 */

/**
 * Get mobile core navigation items (for bottom tab bar)
 *
 * Optimized for 4-tab bottom navigation pattern following iOS/Android guidelines.
 * Contains only the most essential features for quick access.
 *
 * @param familyId - Family ID for generating URLs
 * @returns Array of core mobile navigation items (max 3, + "More" makes 4)
 */
export function getMobileCoreNavigation(familyId?: string): NavigationItem[] {
    if (!familyId) return [];

    return [
        {
            name: 'Dashboard',
            href: `/families/${familyId}`,
            icon: Home,
            description: 'Przegląd rodziny',
        },
        {
            name: 'Zadania',
            href: `/families/${familyId}/tasks`,
            icon: CheckSquare,
            description: 'Zarządzaj zadaniami',
        },
        {
            name: 'Kalendarz',
            href: `/families/${familyId}/calendar`,
            icon: Calendar,
            description: 'Planuj wydarzenia',
        },
    ];
}

/**
 * Get mobile extended navigation items (for "More" overlay)
 *
 * Contains additional features not included in core navigation.
 * Displayed in the "More" tab overlay screen.
 *
 * @param familyId - Family ID for generating URLs
 * @returns Array of extended mobile navigation items
 */
export function getMobileExtendedNavigation(familyId?: string): NavigationItem[] {
    if (!familyId) return [];

    return [
        {
            name: 'Statystyki',
            href: `/families/${familyId}/stats`,
            icon: BarChart3,
            description: 'Analizuj postępy',
        },
        {
            name: 'Członkowie',
            href: `/families/${familyId}/members`,
            icon: Users,
            description: 'Członkowie rodziny',
        },
        {
            name: 'Zaproszenia',
            href: `/families/${familyId}/invitations`,
            icon: Clock,
            description: 'Zarządzaj zaproszeniami',
        },
        {
            name: 'Ustawienia',
            href: `/families/${familyId}/settings`,
            icon: Settings,
            description: 'Konfiguracja rodziny',
        },
        {
            name: 'Profil',
            href: '/settings',
            icon: User,
            description: 'Ustawienia osobiste',
        },
        {
            name: 'Wyloguj się',
            href: '/auth/sign-out',
            icon: LogOut,
            description: 'Zakończ sesję',
        },
    ];
}

/**
 * Get primary navigation items for a family (LEGACY - for backward compatibility)
 *
 * Used by desktop sidebar and as the main navigation section.
 * Limited to 5 items for optimal mobile UX.
 *
 * @param familyId - Family ID for generating URLs
 * @returns Array of primary navigation items
 */
export function getPrimaryNavigation(familyId?: string): NavigationItem[] {
    if (!familyId) return [];

    return [
        {
            name: 'Dashboard',
            href: `/families/${familyId}`,
            icon: Home,
            description: 'Przegląd rodziny',
        },
        {
            name: 'Zadania',
            href: `/families/${familyId}/tasks`,
            icon: CheckSquare,
            description: 'Zarządzaj zadaniami',
        },
        {
            name: 'Kalendarz',
            href: `/families/${familyId}/calendar`,
            icon: Calendar,
            description: 'Planuj wydarzenia',
        },
        {
            name: 'Statystyki',
            href: `/families/${familyId}/stats`,
            icon: BarChart3,
            description: 'Analizuj postępy',
        },
        {
            name: 'Członkowie',
            href: `/families/${familyId}/members`,
            icon: Users,
            description: 'Członkowie rodziny',
        },
    ];
}

/**
 * Get secondary navigation items for a family
 *
 * Used by sidebar management section and hamburger menu.
 * Contains settings and administrative functions.
 *
 * @param familyId - Family ID for generating URLs
 * @returns Array of secondary navigation items
 */
export function getSecondaryNavigation(familyId?: string): NavigationItem[] {
    if (!familyId) return [];

    return [
        {
            name: 'Zaproszenia',
            href: `/families/${familyId}/invitations`,
            icon: Clock,
            description: 'Zarządzaj zaproszeniami',
        },
        {
            name: 'Ustawienia',
            href: `/families/${familyId}/settings`,
            icon: Settings,
            description: 'Konfiguracja rodziny',
        },
        {
            name: 'Profil',
            href: '/settings',
            icon: User,
            description: 'Ustawienia osobiste',
        },
    ];
}

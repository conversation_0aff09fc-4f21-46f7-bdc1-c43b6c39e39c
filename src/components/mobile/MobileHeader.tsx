'use client';

import { useState } from 'react';
import Link from 'next/link';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { UserProfile } from '@/components/auth/UserProfile';
import { FamilySwitcher } from '@/components/family/FamilySwitcher';
import {
    Navigation,
    getPrimaryNavigation,
    getSecondaryNavigation,
} from '@/components/layout/Navigation';
import { Menu, X, ArrowLeft, Bell, Search } from 'lucide-react';

interface MobileHeaderProps {
    title?: string;
    showBack?: boolean;
    backHref?: string;
    user?: any;
    families?: any[];
    currentFamilyId?: string;
    className?: string;
    actions?: React.ReactNode;
    notifications?: number;
    onBackClick?: () => void;
}

export function MobileHeader({
    title,
    showBack = false,
    backHref = '/',
    user,
    families = [],
    currentFamilyId,
    className,
    actions,
    notifications = 0,
    onBackClick,
}: MobileHeaderProps) {
    const [isMenuOpen, setIsMenuOpen] = useState(false);

    const toggleMenu = () => setIsMenuOpen(!isMenuOpen);

    const handleBackClick = () => {
        if (onBackClick) {
            onBackClick();
        }
    };

    return (
        <>
            {/* Main Header */}
            <header
                className={cn(
                    'sticky top-0 z-40 md:hidden',
                    'bg-white border-b border-gray-200',
                    'px-4 py-3',
                    className
                )}
            >
                <div className="flex items-center justify-between">
                    {/* Left Side */}
                    <div className="flex items-center gap-3">
                        {showBack ? (
                            <Button
                                variant="ghost"
                                size="sm"
                                onClick={handleBackClick}
                                asChild={!!backHref}
                                className="p-3 min-h-[44px] min-w-[44px]"
                            >
                                {backHref ? (
                                    <Link href={backHref}>
                                        <ArrowLeft className="h-5 w-5" />
                                    </Link>
                                ) : (
                                    <ArrowLeft className="h-5 w-5" />
                                )}
                            </Button>
                        ) : (
                            <Button
                                variant="ghost"
                                size="sm"
                                onClick={toggleMenu}
                                className="p-3 min-h-[44px] min-w-[44px]"
                            >
                                <Menu className="h-5 w-5" />
                            </Button>
                        )}

                        {/* Title or Logo */}
                        {title ? (
                            <h1 className="text-lg font-semibold text-gray-900 truncate">{title}</h1>
                        ) : (
                            <Link href="/" className="flex items-center">
                                <div className="text-lg font-bold font-display">
                                    <span style={{ color: 'var(--family-purple)' }}>FT</span>
                                    <span className="text-gray-900 ml-1">FamilyTasks</span>
                                </div>
                            </Link>
                        )}
                    </div>

                    {/* Right Side */}
                    <div className="flex items-center gap-2">
                        {actions}

                        {/* Notifications */}
                        {notifications > 0 && (
                            <Button variant="ghost" size="sm" className="relative p-3 min-h-[44px] min-w-[44px]">
                                <Bell className="h-5 w-5" />
                                <div className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                                    {notifications > 99 ? '99+' : notifications}
                                </div>
                            </Button>
                        )}

                        {/* User Avatar (smaller on mobile) */}
                        {user && <UserProfile className="scale-90" />}
                    </div>
                </div>
            </header>

            {/* Mobile Menu Overlay */}
            {isMenuOpen && (
                <div className="fixed inset-0 z-50 md:hidden">
                    {/* Backdrop */}
                    <div className="absolute inset-0 bg-black bg-opacity-50" onClick={toggleMenu} />

                    {/* Menu Panel */}
                    <div className="absolute left-0 top-0 bottom-0 w-80 max-w-[85vw] bg-white shadow-xl">
                        {/* Menu Header */}
                        <div className="flex items-center justify-between p-4 border-b border-gray-200">
                            <div className="text-lg font-bold font-display">
                                <span style={{ color: 'var(--family-purple)' }}>FT</span>
                                <span className="text-gray-900 ml-1">FamilyTasks</span>
                            </div>
                            <Button
                                variant="ghost"
                                size="sm"
                                onClick={toggleMenu}
                                className="p-3 min-h-[44px] min-w-[44px]"
                            >
                                <X className="h-5 w-5" />
                            </Button>
                        </div>

                        {/* Menu Content */}
                        <div className="flex flex-col h-full overflow-y-auto">
                            {/* User Section */}
                            {user && (
                                <div className="p-4 border-b border-gray-200">
                                    <UserProfile showName />
                                </div>
                            )}

                            {/* Family Switcher */}
                            {user && families.length > 0 && (
                                <div className="p-4 border-b border-gray-200">
                                    <div className="text-sm font-medium text-gray-700 mb-2">Rodzina</div>
                                    <FamilySwitcher families={families} currentFamilyId={currentFamilyId} />
                                </div>
                            )}

                            {/* Navigation */}
                            <div className="flex-1 p-4 space-y-6">
                                {/* Primary Navigation */}
                                {currentFamilyId && (
                                    <div>
                                        <div className="text-sm font-medium text-gray-700 mb-3">Menu główne</div>
                                        <Navigation
                                            familyId={currentFamilyId}
                                            families={families}
                                            variant="mobile"
                                            onItemClick={toggleMenu}
                                        />
                                    </div>
                                )}

                                {/* Secondary Navigation */}
                                {currentFamilyId && getSecondaryNavigation(currentFamilyId).length > 0 && (
                                    <div>
                                        <div className="text-sm font-medium text-gray-700 mb-3">Zarządzanie</div>
                                        <nav className="space-y-1">
                                            {getSecondaryNavigation(currentFamilyId).map(item => (
                                                <Link
                                                    key={item.name}
                                                    href={item.href}
                                                    onClick={toggleMenu}
                                                    className="flex items-center gap-2 px-2 pb-2 text-base font-medium text-gray-700 hover:text-purple-600 hover:bg-purple-50 rounded-md transition-colors"
                                                >
                                                    <item.icon className="h-5 w-5 shrink-0" />
                                                    <div className="flex-1 min-w-0">
                                                        <div>{item.name}</div>
                                                        {item.description && (
                                                            <div className="text-xs text-gray-500 mt-1">{item.description}</div>
                                                        )}
                                                    </div>
                                                </Link>
                                            ))}
                                        </nav>
                                    </div>
                                )}

                                {/* No family fallback */}
                                {!currentFamilyId && (
                                    <Navigation
                                        familyId={currentFamilyId}
                                        families={families}
                                        variant="mobile"
                                        onItemClick={toggleMenu}
                                    />
                                )}
                            </div>

                            {/* Auth Section (if not logged in) */}
                            {!user && (
                                <div className="p-4 border-t border-gray-200 space-y-2">
                                    <Button variant="ghost" className="w-full" asChild>
                                        <Link href="/sign-in" onClick={toggleMenu}>
                                            Zaloguj się
                                        </Link>
                                    </Button>
                                    <Button className="w-full" asChild>
                                        <Link href="/sign-up" onClick={toggleMenu}>
                                            Rozpocznij za darmo
                                        </Link>
                                    </Button>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            )}
        </>
    );
}

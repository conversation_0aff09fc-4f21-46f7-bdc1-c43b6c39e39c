'use client';

import { useState } from 'react';
import { usePathname } from 'next/navigation';
import Link from 'next/link';
import { cn } from '@/lib/utils';
import { getMobileCoreNavigation } from '@/components/layout/Navigation';
import { MobileMoreScreen } from '@/components/mobile/MobileMoreScreen';
import { MoreHorizontal } from 'lucide-react';

interface TabItem {
    name: string;
    href: string;
    icon: any;
    badge?: string | number;
}

interface MobileTabBarProps {
    familyId?: string;
    className?: string;
}

export function MobileTabBar({ familyId, className }: MobileTabBarProps) {
    const pathname = usePathname();
    const [isMoreOpen, setIsMoreOpen] = useState(false);

    if (!familyId) return null;

    // Get mobile core navigation (3 main items)
    const coreNav = getMobileCoreNavigation(familyId);

    // Convert to tab format with optimized names for mobile
    const coreTabs: TabItem[] = coreNav.map(item => ({
        name: item.name === 'Dashboard' ? 'Home' : item.name,
        href: item.href,
        icon: item.icon,
    }));

    // Calculate badge count for More tab (example: count of extended features)
    const extendedNavCount = 5; // From getMobileExtendedNavigation
    const moreBadgeCount = extendedNavCount > 3 ? extendedNavCount : undefined;

    // Add "More" tab as the 4th item
    const tabs: TabItem[] = [
        ...coreTabs,
        {
            name: 'Więcej',
            href: '#more', // Special href for More tab
            icon: MoreHorizontal,
            badge: moreBadgeCount,
        },
    ];

    const isActive = (href: string) => {
        if (href === '#more') return false; // More tab is never "active"
        if (href === `/families/${familyId}`) {
            return pathname === href;
        }
        return pathname.startsWith(href);
    };

    const handleMoreClick = () => {
        setIsMoreOpen(true);
    };

    return (
        <>
            <div
                className={cn(
                    'fixed bottom-0 left-0 right-0 z-40 md:hidden',
                    'bg-white border-t border-gray-200',
                    'safe-area-pb', // Safe area padding for iOS
                    className
                )}
            >
                <nav className="flex">
                    {tabs.map(tab => {
                        const active = isActive(tab.href);
                        const isMoreTab = tab.href === '#more';

                        // Render More tab as button, others as Link
                        if (isMoreTab) {
                            return (
                                <button
                                    key={tab.name}
                                    onClick={handleMoreClick}
                                    className={cn(
                                        'flex-1 flex flex-col items-center justify-center',
                                        'py-2 px-1 min-h-[64px] relative',
                                        'transition-colors duration-200',
                                        'active:bg-gray-50', // Touch feedback
                                        'text-gray-500 hover:text-gray-700',
                                        'touch-manipulation' // Optimize for touch
                                    )}
                                >
                                    <div className="relative">
                                        <tab.icon className="h-6 w-6 mb-1" />
                                        {tab.badge && (
                                            <div className="absolute -top-1 -right-1 bg-purple-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                                                {tab.badge}
                                            </div>
                                        )}
                                    </div>
                                    <span className="text-xs font-medium truncate">{tab.name}</span>
                                </button>
                            );
                        }

                        return (
                            <Link
                                key={tab.name}
                                href={tab.href}
                                className={cn(
                                    'flex-1 flex flex-col items-center justify-center',
                                    'py-2 px-1 min-h-[64px] relative',
                                    'transition-colors duration-200',
                                    'active:bg-gray-50', // Touch feedback
                                    active ? 'text-purple-600' : 'text-gray-500 hover:text-gray-700'
                                )}
                            >
                                <div className="relative">
                                    <tab.icon
                                        className={cn('h-6 w-6 mb-1', active && 'scale-110 transition-transform')}
                                    />
                                    {tab.badge && (
                                        <div className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                                            {tab.badge}
                                        </div>
                                    )}
                                </div>
                                <span className={cn('text-xs font-medium truncate', active && 'font-semibold')}>
                                    {tab.name}
                                </span>

                                {/* Active indicator */}
                                {active && (
                                    <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-8 h-1 bg-purple-600 rounded-full" />
                                )}
                            </Link>
                        );
                    })}
                </nav>
            </div>

            {/* More Screen Overlay */}
            <MobileMoreScreen
                familyId={familyId}
                isOpen={isMoreOpen}
                onClose={() => setIsMoreOpen(false)}
            />
        </>
    );
}

import {
  createTaskSchema,
  updateTaskSchema,
  completeTaskSchema,
  verifyTaskSchema,
  taskFiltersSchema,
  validateTaskTitle,
  validateTaskPoints,
  isValidTaskWeight,
  isValidTaskFrequency,
  isValidTaskStatus,
  getPointsForWeight,
  isRecurringTask,
  canTaskBeCompleted,
  canTaskBeAssigned,
  isTaskOverdue,
  isTaskDueSoon,
} from '@/lib/validations/task';
import { TaskWeight, TaskFrequency, TaskStatus } from '@prisma/client';

describe('Task validation schemas', () => {
  describe('createTaskSchema', () => {
    it('should validate valid task data', () => {
      const validData = {
        title: 'Clean room',
        description: 'Clean and organize bedroom',
        points: 5,
        weight: TaskWeight.NORMAL,
        frequency: TaskFrequency.WEEKLY,
        dueDate: new Date('2024-07-01'),
        assignedMemberIds: ['clw123456789012345678901'],
      };

      expect(() => createTaskSchema.parse(validData)).not.toThrow();
    });

    it('should reject empty title', () => {
      const invalidData = {
        title: '',
        points: 1,
        weight: TaskWeight.LIGHT,
        frequency: TaskFrequency.ONCE,
      };

      expect(() => createTaskSchema.parse(invalidData)).toThrow('Tytuł zadania jest wymagany');
    });

    it('should reject title that is too long', () => {
      const invalidData = {
        title: 'a'.repeat(201),
        points: 1,
        weight: TaskWeight.LIGHT,
        frequency: TaskFrequency.ONCE,
      };

      expect(() => createTaskSchema.parse(invalidData)).toThrow(
        'Tytuł zadania nie może być dłuższy niż 200 znaków'
      );
    });

    it('should reject invalid points', () => {
      const invalidData = {
        title: 'Valid title',
        points: 0,
        weight: TaskWeight.LIGHT,
        frequency: TaskFrequency.ONCE,
      };

      expect(() => createTaskSchema.parse(invalidData)).toThrow(
        'Zadanie musi mieć przynajmniej 1 punkt'
      );
    });

    it('should reject points above maximum', () => {
      const invalidData = {
        title: 'Valid title',
        points: 101,
        weight: TaskWeight.LIGHT,
        frequency: TaskFrequency.ONCE,
      };

      expect(() => createTaskSchema.parse(invalidData)).toThrow(
        'Zadanie nie może mieć więcej niż 100 punktów'
      );
    });

    it('should reject non-integer points', () => {
      const invalidData = {
        title: 'Valid title',
        points: 5.5,
        weight: TaskWeight.LIGHT,
        frequency: TaskFrequency.ONCE,
      };

      expect(() => createTaskSchema.parse(invalidData)).toThrow(
        'Punkty muszą być liczbą całkowitą'
      );
    });

    it('should reject invalid weight', () => {
      const invalidData = {
        title: 'Valid title',
        points: 5,
        weight: 'INVALID_WEIGHT' as TaskWeight,
        frequency: TaskFrequency.ONCE,
      };

      expect(() => createTaskSchema.parse(invalidData)).toThrow('Nieprawidłowa waga zadania');
    });

    it('should reject invalid frequency', () => {
      const invalidData = {
        title: 'Valid title',
        points: 5,
        weight: TaskWeight.NORMAL,
        frequency: 'INVALID_FREQUENCY' as TaskFrequency,
      };

      expect(() => createTaskSchema.parse(invalidData)).toThrow(
        'Nieprawidłowa częstotliwość zadania'
      );
    });

    it('should accept optional fields', () => {
      const minimalData = {
        title: 'Minimal task',
        points: 1,
        weight: TaskWeight.LIGHT,
        frequency: TaskFrequency.ONCE,
      };

      expect(() => createTaskSchema.parse(minimalData)).not.toThrow();
    });
  });

  describe('updateTaskSchema', () => {
    it('should validate partial update data', () => {
      const partialData = {
        title: 'Updated title',
        points: 10,
      };

      expect(() => updateTaskSchema.parse(partialData)).not.toThrow();
    });

    it('should accept status in update', () => {
      const dataWithStatus = {
        status: TaskStatus.COMPLETED,
      };

      expect(() => updateTaskSchema.parse(dataWithStatus)).not.toThrow();
    });

    it('should reject invalid status', () => {
      const invalidData = {
        status: 'INVALID_STATUS' as TaskStatus,
      };

      expect(() => updateTaskSchema.parse(invalidData)).toThrow('Nieprawidłowy status zadania');
    });
  });

  describe('completeTaskSchema', () => {
    it('should validate completion with notes', () => {
      const validData = {
        notes: 'Task completed successfully',
      };

      expect(() => completeTaskSchema.parse(validData)).not.toThrow();
    });

    it('should validate completion without notes', () => {
      const validData = {};

      expect(() => completeTaskSchema.parse(validData)).not.toThrow();
    });

    it('should reject notes that are too long', () => {
      const invalidData = {
        notes: 'a'.repeat(501),
      };

      expect(() => completeTaskSchema.parse(invalidData)).toThrow(
        'Notatki nie mogą być dłuższe niż 500 znaków'
      );
    });
  });

  describe('verifyTaskSchema', () => {
    it('should validate approval with feedback', () => {
      const validData = {
        approved: true,
        feedback: 'Great job!',
      };

      expect(() => verifyTaskSchema.parse(validData)).not.toThrow();
    });

    it('should validate rejection without feedback', () => {
      const validData = {
        approved: false,
      };

      expect(() => verifyTaskSchema.parse(validData)).not.toThrow();
    });

    it('should reject feedback that is too long', () => {
      const invalidData = {
        approved: true,
        feedback: 'a'.repeat(501),
      };

      expect(() => verifyTaskSchema.parse(invalidData)).toThrow(
        'Feedback nie może być dłuższy niż 500 znaków'
      );
    });
  });

  describe('taskFiltersSchema', () => {
    it('should validate complete filters', () => {
      const validFilters = {
        status: [TaskStatus.ACTIVE, TaskStatus.COMPLETED],
        assignedMemberIds: ['clw123456789012345678901'],
        dueDate: {
          from: new Date('2024-06-01'),
          to: new Date('2024-06-30'),
        },
        weight: [TaskWeight.NORMAL, TaskWeight.HEAVY],
        frequency: [TaskFrequency.DAILY, TaskFrequency.WEEKLY],
      };

      expect(() => taskFiltersSchema.parse(validFilters)).not.toThrow();
    });

    it('should validate empty filters', () => {
      const emptyFilters = {};

      expect(() => taskFiltersSchema.parse(emptyFilters)).not.toThrow();
    });

    it('should validate partial filters', () => {
      const partialFilters = {
        status: [TaskStatus.ACTIVE],
      };

      expect(() => taskFiltersSchema.parse(partialFilters)).not.toThrow();
    });
  });
});

describe('Task validation helpers', () => {
  describe('validateTaskTitle', () => {
    it('should return true for valid titles', () => {
      expect(validateTaskTitle('Valid title')).toBe(true);
      expect(validateTaskTitle('   Trimmed title   ')).toBe(true);
    });

    it('should return false for invalid titles', () => {
      expect(validateTaskTitle('')).toBe(false);
      expect(validateTaskTitle('   ')).toBe(false);
      expect(validateTaskTitle('a'.repeat(201))).toBe(false);
    });
  });

  describe('validateTaskPoints', () => {
    it('should return true for valid points', () => {
      expect(validateTaskPoints(1)).toBe(true);
      expect(validateTaskPoints(50)).toBe(true);
      expect(validateTaskPoints(100)).toBe(true);
    });

    it('should return false for invalid points', () => {
      expect(validateTaskPoints(0)).toBe(false);
      expect(validateTaskPoints(101)).toBe(false);
      expect(validateTaskPoints(5.5)).toBe(false);
    });
  });

  describe('isValidTaskWeight', () => {
    it('should return true for valid weights', () => {
      expect(isValidTaskWeight(TaskWeight.LIGHT)).toBe(true);
      expect(isValidTaskWeight(TaskWeight.NORMAL)).toBe(true);
      expect(isValidTaskWeight(TaskWeight.HEAVY)).toBe(true);
      expect(isValidTaskWeight(TaskWeight.CRITICAL)).toBe(true);
    });

    it('should return false for invalid weights', () => {
      expect(isValidTaskWeight('INVALID')).toBe(false);
      expect(isValidTaskWeight('')).toBe(false);
    });
  });

  describe('isValidTaskFrequency', () => {
    it('should return true for valid frequencies', () => {
      expect(isValidTaskFrequency(TaskFrequency.ONCE)).toBe(true);
      expect(isValidTaskFrequency(TaskFrequency.DAILY)).toBe(true);
      expect(isValidTaskFrequency(TaskFrequency.WEEKLY)).toBe(true);
      expect(isValidTaskFrequency(TaskFrequency.MONTHLY)).toBe(true);
    });

    it('should return false for invalid frequencies', () => {
      expect(isValidTaskFrequency('INVALID')).toBe(false);
      expect(isValidTaskFrequency('')).toBe(false);
    });
  });

  describe('isValidTaskStatus', () => {
    it('should return true for valid statuses', () => {
      expect(isValidTaskStatus(TaskStatus.ACTIVE)).toBe(true);
      expect(isValidTaskStatus(TaskStatus.COMPLETED)).toBe(true);
      expect(isValidTaskStatus(TaskStatus.CANCELLED)).toBe(true);
      expect(isValidTaskStatus(TaskStatus.SUSPENDED)).toBe(true);
    });

    it('should return false for invalid statuses', () => {
      expect(isValidTaskStatus('INVALID')).toBe(false);
      expect(isValidTaskStatus('')).toBe(false);
    });
  });

  describe('getPointsForWeight', () => {
    it('should return correct points for each weight', () => {
      expect(getPointsForWeight(TaskWeight.LIGHT)).toBe(1);
      expect(getPointsForWeight(TaskWeight.NORMAL)).toBe(3);
      expect(getPointsForWeight(TaskWeight.HEAVY)).toBe(5);
      expect(getPointsForWeight(TaskWeight.CRITICAL)).toBe(10);
    });
  });

  describe('isRecurringTask', () => {
    it('should return true for recurring frequencies', () => {
      expect(isRecurringTask(TaskFrequency.DAILY)).toBe(true);
      expect(isRecurringTask(TaskFrequency.WEEKLY)).toBe(true);
      expect(isRecurringTask(TaskFrequency.MONTHLY)).toBe(true);
    });

    it('should return false for one-time tasks', () => {
      expect(isRecurringTask(TaskFrequency.ONCE)).toBe(false);
    });
  });

  describe('canTaskBeCompleted', () => {
    it('should return true for active tasks', () => {
      expect(canTaskBeCompleted(TaskStatus.ACTIVE)).toBe(true);
    });

    it('should return false for non-active tasks', () => {
      expect(canTaskBeCompleted(TaskStatus.COMPLETED)).toBe(false);
      expect(canTaskBeCompleted(TaskStatus.CANCELLED)).toBe(false);
      expect(canTaskBeCompleted(TaskStatus.SUSPENDED)).toBe(false);
    });
  });

  describe('canTaskBeAssigned', () => {
    it('should return true for active tasks', () => {
      expect(canTaskBeAssigned(TaskStatus.ACTIVE)).toBe(true);
    });

    it('should return false for non-active tasks', () => {
      expect(canTaskBeAssigned(TaskStatus.COMPLETED)).toBe(false);
      expect(canTaskBeAssigned(TaskStatus.CANCELLED)).toBe(false);
      expect(canTaskBeAssigned(TaskStatus.SUSPENDED)).toBe(false);
    });
  });

  describe('isTaskOverdue', () => {
    beforeEach(() => {
      jest.useFakeTimers();
      jest.setSystemTime(new Date('2024-06-25T12:00:00'));
    });

    afterEach(() => {
      jest.useRealTimers();
    });

    it('should return false for null due date', () => {
      expect(isTaskOverdue(null)).toBe(false);
    });

    it('should return true for past due date', () => {
      const pastDate = new Date('2024-06-24T12:00:00');
      expect(isTaskOverdue(pastDate)).toBe(true);
    });

    it('should return false for future due date', () => {
      const futureDate = new Date('2024-06-26T12:00:00');
      expect(isTaskOverdue(futureDate)).toBe(false);
    });
  });

  describe('isTaskDueSoon', () => {
    beforeEach(() => {
      jest.useFakeTimers();
      jest.setSystemTime(new Date('2024-06-25T12:00:00'));
    });

    afterEach(() => {
      jest.useRealTimers();
    });

    it('should return false for null due date', () => {
      expect(isTaskDueSoon(null)).toBe(false);
    });

    it('should return true for due date within threshold', () => {
      const soonDate = new Date('2024-06-26T10:00:00'); // 22 hours from now
      expect(isTaskDueSoon(soonDate, 24)).toBe(true);
    });

    it('should return false for due date beyond threshold', () => {
      const laterDate = new Date('2024-06-27T14:00:00'); // 26 hours from now
      expect(isTaskDueSoon(laterDate, 24)).toBe(false);
    });

    it('should return false for past due date', () => {
      const pastDate = new Date('2024-06-24T12:00:00');
      expect(isTaskDueSoon(pastDate)).toBe(false);
    });

    it('should use default threshold of 24 hours', () => {
      const soonDate = new Date('2024-06-26T10:00:00'); // 22 hours from now
      expect(isTaskDueSoon(soonDate)).toBe(true);
    });
  });
});

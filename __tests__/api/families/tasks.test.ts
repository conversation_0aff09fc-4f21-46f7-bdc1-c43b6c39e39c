import { createMocks } from 'node-mocks-http';
import { NextRequest } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { getFamilyMemberWithRole, hasPermission } from '@/lib/auth';
import { TaskService } from '@/lib/services/task.service';
import { mockTask, mockMember } from '@/lib/test-utils/mocks';

// Mock dependencies
jest.mock('@clerk/nextjs/server');
jest.mock('@/lib/auth');
jest.mock('@/lib/services/task.service');

const mockAuth = auth as jest.MockedFunction<typeof auth>;
const mockGetFamilyMemberWithRole = getFamilyMemberWithRole as jest.MockedFunction<
  typeof getFamilyMemberWithRole
>;
const mockHasPermission = hasPermission as jest.MockedFunction<typeof hasPermission>;

// We need to mock the actual route handler
const mockRouteHandler = {
  GET: jest.fn(),
  POST: jest.fn(),
};

describe('/api/families/[familyId]/tasks', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('GET endpoint', () => {
    it('should return 401 when user is not authenticated', async () => {
      mockAuth.mockResolvedValue({ userId: null });

      const { req } = createMocks({
        method: 'GET',
        url: '/api/families/family-1/tasks',
      });

      // Mock the route handler behavior
      const mockResponse = { json: jest.fn(), status: jest.fn() };
      mockRouteHandler.GET.mockResolvedValue(mockResponse);

      expect(mockAuth).toHaveBeenCalled();
    });

    it('should return 403 when user has no access to family', async () => {
      mockAuth.mockResolvedValue({ userId: 'user-1' });
      mockGetFamilyMemberWithRole.mockResolvedValue(null);

      const { req } = createMocks({
        method: 'GET',
        url: '/api/families/family-1/tasks',
      });

      expect(mockGetFamilyMemberWithRole).toHaveBeenCalledWith('user-1', 'family-1');
    });

    it('should return 403 when user lacks VIEW_TASKS permission', async () => {
      mockAuth.mockResolvedValue({ userId: 'user-1' });
      mockGetFamilyMemberWithRole.mockResolvedValue(mockMember);
      mockHasPermission.mockReturnValue(false);

      const { req } = createMocks({
        method: 'GET',
        url: '/api/families/family-1/tasks',
      });

      expect(mockHasPermission).toHaveBeenCalledWith(mockMember.role, 'VIEW_TASKS');
    });

    it('should return family tasks for authorized user', async () => {
      mockAuth.mockResolvedValue({ userId: 'user-1' });
      mockGetFamilyMemberWithRole.mockResolvedValue(mockMember);
      mockHasPermission.mockReturnValue(true);

      const mockGetFamilyTasks = jest.spyOn(TaskService, 'getFamilyTasks');
      mockGetFamilyTasks.mockResolvedValue([mockTask]);

      const { req } = createMocks({
        method: 'GET',
        url: '/api/families/family-1/tasks',
      });

      expect(mockGetFamilyTasks).toHaveBeenCalledWith('family-1', undefined);
    });

    it('should apply status filter from query params', async () => {
      mockAuth.mockResolvedValue({ userId: 'user-1' });
      mockGetFamilyMemberWithRole.mockResolvedValue(mockMember);
      mockHasPermission.mockReturnValue(true);

      const mockGetFamilyTasks = jest.spyOn(TaskService, 'getFamilyTasks');
      mockGetFamilyTasks.mockResolvedValue([mockTask]);

      const { req } = createMocks({
        method: 'GET',
        url: '/api/families/family-1/tasks?status=ACTIVE,COMPLETED',
      });

      // Verify that filters would be applied
      expect(mockAuth).toHaveBeenCalled();
    });

    it('should apply assignedMemberIds filter from query params', async () => {
      mockAuth.mockResolvedValue({ userId: 'user-1' });
      mockGetFamilyMemberWithRole.mockResolvedValue(mockMember);
      mockHasPermission.mockReturnValue(true);

      const mockGetFamilyTasks = jest.spyOn(TaskService, 'getFamilyTasks');
      mockGetFamilyTasks.mockResolvedValue([mockTask]);

      const { req } = createMocks({
        method: 'GET',
        url: '/api/families/family-1/tasks?assignedMemberIds=member-1',
      });

      expect(mockAuth).toHaveBeenCalled();
    });
  });

  describe('POST endpoint', () => {
    it('should return 401 when user is not authenticated', async () => {
      mockAuth.mockResolvedValue({ userId: null });

      const { req } = createMocks({
        method: 'POST',
        url: '/api/families/family-1/tasks',
        body: {
          title: 'New Task',
          weight: 'NORMAL',
          frequency: 'ONCE',
          points: 3,
        },
      });

      expect(mockAuth).toHaveBeenCalled();
    });

    it('should return 403 when user lacks CREATE_TASKS permission', async () => {
      mockAuth.mockResolvedValue({ userId: 'user-1' });
      mockGetFamilyMemberWithRole.mockResolvedValue(mockMember);
      mockHasPermission.mockReturnValue(false);

      const { req } = createMocks({
        method: 'POST',
        url: '/api/families/family-1/tasks',
        body: {
          title: 'New Task',
          weight: 'NORMAL',
          frequency: 'ONCE',
          points: 3,
        },
      });

      expect(mockHasPermission).toHaveBeenCalledWith(mockMember.role, 'CREATE_TASKS');
    });

    it('should validate request body', async () => {
      mockAuth.mockResolvedValue({ userId: 'user-1' });
      mockGetFamilyMemberWithRole.mockResolvedValue(mockMember);
      mockHasPermission.mockReturnValue(true);

      const { req } = createMocks({
        method: 'POST',
        url: '/api/families/family-1/tasks',
        body: {
          title: '', // Invalid empty title
          weight: 'NORMAL',
          frequency: 'ONCE',
          points: 3,
        },
      });

      // Validation would reject empty title
      expect(mockAuth).toHaveBeenCalled();
    });

    it('should create task with valid data', async () => {
      mockAuth.mockResolvedValue({ userId: 'user-1' });
      mockGetFamilyMemberWithRole.mockResolvedValue(mockMember);
      mockHasPermission.mockReturnValue(true);

      const mockCreateTask = jest.spyOn(TaskService, 'createTask');
      mockCreateTask.mockResolvedValue(mockTask);

      const validTaskData = {
        title: 'New Task',
        description: 'Task description',
        weight: 'NORMAL',
        frequency: 'ONCE',
        points: 3,
        assignedMemberIds: ['member-1'],
      };

      const { req } = createMocks({
        method: 'POST',
        url: '/api/families/family-1/tasks',
        body: validTaskData,
      });

      expect(mockCreateTask).toHaveBeenCalledWith(
        'family-1',
        mockMember.id,
        expect.objectContaining({
          title: validTaskData.title,
          description: validTaskData.description,
          points: validTaskData.points,
        })
      );
    });

    it('should handle task creation errors', async () => {
      mockAuth.mockResolvedValue({ userId: 'user-1' });
      mockGetFamilyMemberWithRole.mockResolvedValue(mockMember);
      mockHasPermission.mockReturnValue(true);

      const mockCreateTask = jest.spyOn(TaskService, 'createTask');
      mockCreateTask.mockRejectedValue(new Error('Database error'));

      const { req } = createMocks({
        method: 'POST',
        url: '/api/families/family-1/tasks',
        body: {
          title: 'New Task',
          weight: 'NORMAL',
          frequency: 'ONCE',
          points: 3,
        },
      });

      expect(mockCreateTask).toHaveBeenCalled();
    });
  });

  describe('Error handling', () => {
    it('should handle database connection errors', async () => {
      mockAuth.mockResolvedValue({ userId: 'user-1' });
      mockGetFamilyMemberWithRole.mockRejectedValue(new Error('Database connection failed'));

      const { req } = createMocks({
        method: 'GET',
        url: '/api/families/family-1/tasks',
      });

      expect(mockGetFamilyMemberWithRole).toHaveBeenCalled();
    });

    it('should handle invalid family ID format', async () => {
      mockAuth.mockResolvedValue({ userId: 'user-1' });

      const { req } = createMocks({
        method: 'GET',
        url: '/api/families/invalid-family-id/tasks',
      });

      expect(mockAuth).toHaveBeenCalled();
    });
  });

  describe('Query parameter parsing', () => {
    it('should ignore invalid status values', async () => {
      mockAuth.mockResolvedValue({ userId: 'user-1' });
      mockGetFamilyMemberWithRole.mockResolvedValue(mockMember);
      mockHasPermission.mockReturnValue(true);

      const mockGetFamilyTasks = jest.spyOn(TaskService, 'getFamilyTasks');
      mockGetFamilyTasks.mockResolvedValue([]);

      const { req } = createMocks({
        method: 'GET',
        url: '/api/families/family-1/tasks?status=INVALID_STATUS,ACTIVE',
      });

      // Should filter out invalid status values
      expect(mockAuth).toHaveBeenCalled();
    });

    it('should handle date range filters', async () => {
      mockAuth.mockResolvedValue({ userId: 'user-1' });
      mockGetFamilyMemberWithRole.mockResolvedValue(mockMember);
      mockHasPermission.mockReturnValue(true);

      const mockGetFamilyTasks = jest.spyOn(TaskService, 'getFamilyTasks');
      mockGetFamilyTasks.mockResolvedValue([]);

      const { req } = createMocks({
        method: 'GET',
        url: '/api/families/family-1/tasks?dueDateFrom=2024-06-01&dueDateTo=2024-06-30',
      });

      expect(mockAuth).toHaveBeenCalled();
    });
  });
});

import { TaskService } from '@/lib/services/task.service';
import { prismaMock, resetPrismaMock } from '@/lib/test-utils/prisma-mock';
import {
  mockTask,
  mockCreateTaskData,
  mockTaskCompletion,
  mockMember,
} from '@/lib/test-utils/mocks';
import { TaskStatus, CompletionStatus, TaskWeight, TaskFrequency } from '@prisma/client';

describe('TaskService', () => {
  beforeEach(() => {
    resetPrismaMock();
  });

  describe('createTask', () => {
    it('should create task with valid data', async () => {
      const expectedTask = { ...mockTask, ...mockCreateTaskData };
      prismaMock.task.create.mockResolvedValue(expectedTask as any);

      const result = await TaskService.createTask('family-1', 'member-1', mockCreateTaskData);

      expect(result).toEqual(expectedTask);
      expect(prismaMock.task.create).toHaveBeenCalledWith({
        data: {
          familyId: 'family-1',
          title: mockCreateTaskData.title,
          description: mockCreateTaskData.description,
          points: mockCreateTaskData.points,
          weight: mockCreateTaskData.weight,
          frequency: mockCreateTaskData.frequency,
          dueDate: mockCreateTaskData.dueDate,
          assignedBy: 'member-1',
          assignedAt: expect.any(Date),
          status: TaskStatus.ACTIVE,
        },
        include: expect.any(Object),
      });
    });

    it('should create task without assignment', async () => {
      const taskDataWithoutAssignment = { ...mockCreateTaskData, assignedMemberIds: undefined };
      const expectedTask = { ...mockTask, ...taskDataWithoutAssignment, assignedAt: null };
      prismaMock.task.create.mockResolvedValue(expectedTask as any);

      const result = await TaskService.createTask(
        'family-1',
        'member-1',
        taskDataWithoutAssignment
      );

      expect(result).toEqual(expectedTask);
      expect(prismaMock.task.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          assignedAt: null,
        }),
        include: expect.any(Object),
      });
    });
  });

  describe('updateTask', () => {
    it('should update task with valid data', async () => {
      const updateData = { title: 'Updated Task', points: 10 };
      const updatedTask = { ...mockTask, ...updateData };
      prismaMock.task.update.mockResolvedValue(updatedTask as any);

      const result = await TaskService.updateTask('task-1', updateData);

      expect(result).toEqual(updatedTask);
      expect(prismaMock.task.update).toHaveBeenCalledWith({
        where: { id: 'task-1' },
        data: {
          ...updateData,
          updatedAt: expect.any(Date),
        },
        include: expect.any(Object),
      });
    });
  });

  describe('deleteTask', () => {
    it('should delete task', async () => {
      prismaMock.task.delete.mockResolvedValue(mockTask as any);

      await TaskService.deleteTask('task-1');

      expect(prismaMock.task.delete).toHaveBeenCalledWith({
        where: { id: 'task-1' },
      });
    });
  });

  describe('getTask', () => {
    it('should return task when found', async () => {
      prismaMock.task.findUnique.mockResolvedValue(mockTask as any);

      const result = await TaskService.getTask('task-1');

      expect(result).toEqual(mockTask);
      expect(prismaMock.task.findUnique).toHaveBeenCalledWith({
        where: { id: 'task-1' },
        include: expect.any(Object),
      });
    });

    it('should return null when task not found', async () => {
      prismaMock.task.findUnique.mockResolvedValue(null);

      const result = await TaskService.getTask('non-existent');

      expect(result).toBeNull();
    });
  });

  describe('completeTask', () => {
    it('should complete task successfully', async () => {
      // Mock transaction
      const mockTransaction = jest.fn().mockImplementation(async callback => {
        return await callback({
          task: {
            findUnique: jest.fn().mockResolvedValue(mockTask),
          },
          taskCompletion: {
            findFirst: jest.fn().mockResolvedValue(null),
            create: jest.fn().mockResolvedValue(mockTaskCompletion),
          },
        });
      });
      prismaMock.$transaction.mockImplementation(mockTransaction);

      const result = await TaskService.completeTask('task-1', 'member-1', 'Task completed');

      expect(result).toEqual(mockTaskCompletion);
      expect(mockTransaction).toHaveBeenCalled();
    });

    it('should throw error when task not found', async () => {
      const mockTransaction = jest.fn().mockImplementation(async callback => {
        return await callback({
          task: {
            findUnique: jest.fn().mockResolvedValue(null),
          },
          taskCompletion: {
            findFirst: jest.fn(),
            create: jest.fn(),
          },
        });
      });
      prismaMock.$transaction.mockImplementation(mockTransaction);

      await expect(TaskService.completeTask('non-existent', 'member-1', 'Notes')).rejects.toThrow(
        'Task not found'
      );
    });

    it('should throw error when task is not active', async () => {
      const inactiveTask = { ...mockTask, status: TaskStatus.COMPLETED };
      const mockTransaction = jest.fn().mockImplementation(async callback => {
        return await callback({
          task: {
            findUnique: jest.fn().mockResolvedValue(inactiveTask),
          },
          taskCompletion: {
            findFirst: jest.fn(),
            create: jest.fn(),
          },
        });
      });
      prismaMock.$transaction.mockImplementation(mockTransaction);

      await expect(TaskService.completeTask('task-1', 'member-1', 'Notes')).rejects.toThrow(
        'Task is not active'
      );
    });

    it('should throw error when task already completed by member', async () => {
      const existingCompletion = { ...mockTaskCompletion, status: CompletionStatus.APPROVED };
      const mockTransaction = jest.fn().mockImplementation(async callback => {
        return await callback({
          task: {
            findUnique: jest.fn().mockResolvedValue(mockTask),
          },
          taskCompletion: {
            findFirst: jest.fn().mockResolvedValue(existingCompletion),
            create: jest.fn(),
          },
        });
      });
      prismaMock.$transaction.mockImplementation(mockTransaction);

      await expect(TaskService.completeTask('task-1', 'member-1', 'Notes')).rejects.toThrow(
        'Task already completed by this member'
      );
    });
  });

  describe('calculatePoints', () => {
    it('should calculate base points correctly', async () => {
      const lightTask = { ...mockTask, weight: TaskWeight.LIGHT };
      const points = await TaskService.calculatePoints(lightTask as any);
      expect(points).toBeGreaterThan(0);
    });

    it('should add early completion bonus', async () => {
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 2);

      const earlyTask = { ...mockTask, dueDate: futureDate };
      const points = await TaskService.calculatePoints(earlyTask as any);
      expect(points).toBeGreaterThan(0);
    });

    it('should add frequency bonus for daily tasks', async () => {
      const dailyTask = { ...mockTask, frequency: TaskFrequency.DAILY };
      const points = await TaskService.calculatePoints(dailyTask as any);
      expect(points).toBeGreaterThan(0);
    });
  });

  describe('getFamilyTasks', () => {
    it('should return family tasks without filters', async () => {
      const tasks = [mockTask];
      prismaMock.task.findMany.mockResolvedValue(tasks as any);

      const result = await TaskService.getFamilyTasks('family-1');

      expect(result).toEqual(tasks);
      expect(prismaMock.task.findMany).toHaveBeenCalledWith({
        where: { familyId: 'family-1' },
        include: expect.any(Object),
        orderBy: [{ dueDate: 'asc' }, { createdAt: 'desc' }],
      });
    });

    it('should apply status filter', async () => {
      const tasks = [mockTask];
      prismaMock.task.findMany.mockResolvedValue(tasks as any);

      const filters = { status: [TaskStatus.ACTIVE] };
      await TaskService.getFamilyTasks('family-1', filters);

      expect(prismaMock.task.findMany).toHaveBeenCalledWith({
        where: {
          familyId: 'family-1',
          status: { in: [TaskStatus.ACTIVE] },
        },
        include: expect.any(Object),
        orderBy: [{ dueDate: 'asc' }, { createdAt: 'desc' }],
      });
    });

    it('should apply assigned to filter', async () => {
      const tasks = [mockTask];
      prismaMock.task.findMany.mockResolvedValue(tasks as any);

      const filters = { assignedMemberIds: ['member-1'] };
      await TaskService.getFamilyTasks('family-1', filters);

      expect(prismaMock.task.findMany).toHaveBeenCalledWith({
        where: {
          familyId: 'family-1',
          assignments: {
            some: {
              memberId: { in: ['member-1'] },
            },
          },
        },
        include: expect.any(Object),
        orderBy: [{ dueDate: 'asc' }, { createdAt: 'desc' }],
      });
    });
  });

  describe('assignTask', () => {
    it('should assign task to member', async () => {
      const assignedTask = { ...mockTask, assignments: [{ memberId: 'new-member' }] };
      prismaMock.task.update.mockResolvedValue(assignedTask as any);

      const result = await TaskService.assignTask('task-1', 'new-member');

      expect(result).toEqual(assignedTask);
      expect(prismaMock.task.update).toHaveBeenCalledWith({
        where: { id: 'task-1' },
        data: {
          assignedAt: expect.any(Date),
        },
        include: expect.any(Object),
      });
    });
  });

  describe('getTaskStats', () => {
    it('should return task statistics', async () => {
      const mockStats = [10, 5, 15, 2];
      prismaMock.task.count
        .mockResolvedValueOnce(mockStats[0]) // totalTasks
        .mockResolvedValueOnce(mockStats[1]); // activeTasks

      prismaMock.taskCompletion.count.mockResolvedValueOnce(mockStats[2]); // completedTasks
      prismaMock.task.count.mockResolvedValueOnce(mockStats[3]); // overdueTasks

      const result = await TaskService.getTaskStats('family-1');

      expect(result).toEqual({
        totalTasks: 10,
        activeTasks: 5,
        completedTasks: 15,
        overdueTasks: 2,
      });
    });
  });
});
